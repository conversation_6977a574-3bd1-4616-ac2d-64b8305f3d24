/*
 * Created Date: Monday, 22nd July 2024, 11:44:01
 * Author: gapo
 * -----
 * Last Modified: Monday, 16th September 2024 16:25:13
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../../data/data.dart';
import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketRelativeUseCase extends GPBaseFutureUseCase<TicketRelativeInput,
    ListAPIResponseV2<TicketListResponse>> {
  TicketRelativeUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<ListAPIResponseV2<TicketListResponse>> buildUseCase(
    TicketRelativeInput input,
  ) async {
    return _ticketRepository.relativeTickets(input.params);
  }
}

class TicketRelativeInput extends GPBaseInput {
  const TicketRelativeInput({
    required this.params,
  });

  final TicketListRelativeParams params;
}
